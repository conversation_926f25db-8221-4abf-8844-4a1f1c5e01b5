import { IUserProfileById, UserWatchlistHistory } from '@/apis/user';
import { useGetInfiniteUserWatchedHistoryQuery } from '@/apis/user/queries';
import { Icons } from '@/assets/icons';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { ListRenderItem } from '@shopify/flash-list';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { WatchedItem } from './WatchedItem';
import queryKeys from '@/utils/queryKeys';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  tab: string;
  profile?: IUserProfileById;
};

export const WatchedAll = memo(({ userId, tab, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [listType, setListType] = useState<'list' | 'grid'>('grid');

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: watchlistData,
    isPending,
    isSuccess,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserWatchedHistoryQuery({
    userId: userId ?? '',
    limit: 24,
    type: tab !== 'All' ? (tab === 'Shows' ? 'podcast' : 'episode') : undefined,
  });

  const watchlist = watchlistData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    async (item: UserWatchlistHistory) => {
      if (item.type === 'episode') {
        return episodeDetailDirect(queryClient, item.mediaId.toString());
      }

      await showDetailDirect(queryClient, item.mediaId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserWatchlistHistory>>(
    ({ item }) => <WatchedItem item={item} listType={listType} itemSize={itemSize} onPress={handleDirect} />,
    [handleDirect, listType]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserWatchlistHistory) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerList}>
        <ThemedText type='defaultSemiBold'>Recent</ThemedText>

        {listType === 'list' ? (
          <TouchableOpacity onPress={() => setListType('grid')} activeOpacity={0.7}>
            <Icons.GridIcon size={24} color='#fff' />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setListType('list')} activeOpacity={0.7}>
            <Icons.ListIcon size={24} color='#fff' />
          </TouchableOpacity>
        )}
      </View>
    ),
    [styles, listType]
  );

  const renderSeparator = useCallback(() => <Spacer height={listType === 'list' ? 24 : 28} />, [listType]);

  const handleRefetch = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.userProfile.getUserWatchedHistoryInfinite() });
  }, [queryClient]);

  const isShowEmpty = isSuccess && watchlist.length === 0;

  return (
    <TabFlashList
      key={listType}
      extraData={listType}
      bounces={false}
      refreshing={isPending}
      onRefresh={handleRefetch}
      numColumns={listType === 'list' ? 1 : 3}
      data={watchlist}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeader}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      estimatedItemSize={listType === 'list' ? 64 : itemSize}
      ItemSeparatorComponent={renderSeparator}
      ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='check'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} ${
              tab === 'All'
                ? 'marked any shows or episodes as watched'
                : tab === 'Shows'
                  ? 'marked any shows as watched'
                  : 'marked any episodes as watched'
            }`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  headerList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
  },
}));
