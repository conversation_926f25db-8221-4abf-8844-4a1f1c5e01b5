import { useState } from 'react';
import { View, TouchableOpacity, Linking } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { HeaderStyle } from '@/components/HeaderStyle';
import { ThemedText } from '@/components/ThemedText';
import Ionicons from '@expo/vector-icons/build/Ionicons';
import { Spacer } from '@/components/Spacer';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import Animated from 'react-native-reanimated';

const FAQ_LIST = [
  {
    question: 'What can I do on Rabid?',
    answer:
      'Rabid lets you track podcasts, rate episodes, write reviews, build favorite lists, follow friends, and engage in a podcast-loving community, all in one app.',
  },
  {
    question: 'Can I review individual episodes or just whole shows?',
    answer:
      'You can review both! Rabid supports detailed ratings and comments on individual podcast episodes as well as entire shows.',
  },
  {
    question: 'How do favorites and lists work?',
    answer:
      'You can add shows or episodes to your favorites, then organize them into personal lists—public or private. You can also reorder or delete items anytime.',
  },
  {
    question: 'What is the Activity Feed?',
    answer:
      "The Activity Feed shows recent actions—your own or your friends'—such as added reviews, liked shows, or updates to watchlists.",
  },
  {
    question: 'How does the Watchlist feature work?',
    answer: `Mark episodes or shows you plan to listen to later. Once you’ve finished, mark them as “Watched” to keep track of your journey.`,
  },
  {
    question: 'Can I edit or delete my comments?',
    answer:
      'Only premium member (Founder <PERSON>ron) can edit or delete any of your shows/episodes comments, or replies at any time via your post menu.',
  },
  {
    question: 'What is "Founding Patron" or "Founding Member"?',
    answer:
      'These are exclusive early supporter badges given to users who backed Rabid during its launch phase or premium subscription rollout.',
  },
  {
    question: 'How do community posts work?',
    answer:
      'You can post thoughts, images, or questions in the Community tab. Posts can be general or linked to specific shows or episodes.',
  },
];

const HelpSupportScreen = () => {
  const { styles, theme } = useStyles(stylesheet);
  const [search, setSearch] = useState('');
  const [openIndices, setOpenIndices] = useState<Set<number>>(new Set());

  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  const filteredFAQ = FAQ_LIST.filter((faq) => faq.question.toLowerCase().includes(search.toLowerCase()));

  return (
    <View style={styles.container}>
      <HeaderStyle title='Help & Support' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        onScroll={onScroll}
        contentContainerStyle={{ paddingTop: headerHeight }}
      >
        <ThemedText type='defaultSemiBold' style={styles.sectionTitle}>
          Frequently Asked Questions
        </ThemedText>

        <Spacer height={24} />
        {/* <View style={styles.searchBox}>
          <Icons.Search size={24} color={theme.colors.neutralWhite} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder='Search Question'
            placeholderTextColor={theme.colors.neutralWhite}
            value={search}
            onChangeText={setSearch}
          />
        </View> */}
        <View style={styles.faqList}>
          {filteredFAQ.map((faq, idx) => (
            <View key={faq.question} style={styles.faqItem}>
              <TouchableOpacity
                style={styles.faqHeader}
                activeOpacity={0.7}
                onPress={() => {
                  const newOpenIndices = new Set(openIndices);
                  if (newOpenIndices.has(idx)) {
                    newOpenIndices.delete(idx);
                  } else {
                    newOpenIndices.add(idx);
                  }
                  setOpenIndices(newOpenIndices);
                }}
              >
                <ThemedText type='defaultMedium' style={styles.faqQuestion}>
                  {faq.question}
                </ThemedText>

                <Spacer width={8} />

                <Ionicons
                  name={openIndices.has(idx) ? 'chevron-up' : 'chevron-down'}
                  size={16}
                  color={theme.colors.neutralWhite}
                  style={styles.dropdownIcon}
                />
              </TouchableOpacity>
              {openIndices.has(idx) && (
                <ThemedText type='smallNormal' style={styles.faqAnswer}>
                  {faq.answer}
                </ThemedText>
              )}
            </View>
          ))}
        </View>
        <View style={styles.footerSpace} />
      </Animated.ScrollView>

      <View style={styles.footerContainer}>
        <View style={styles.footerContentRow}>
          <View style={styles.footerTextBoxRow}>
            <ThemedText type='defaultMedium' style={styles.footerText}>
              Looking for feature details? Reach out to our support team via email!
            </ThemedText>
            <ThemedText type='smallSemiBold' style={styles.emailTextRow}>
              <EMAIL>
            </ThemedText>
          </View>
          <TouchableOpacity
            style={styles.emailBtnRow}
            activeOpacity={0.8}
            onPress={() => Linking.openURL('mailto:<EMAIL>')}
          >
            <ThemedText type='smallSemiBold' style={styles.emailBtnTextRow}>
              Email Us
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  sectionTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
    marginBottom: 24,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 9999,
    height: 48,
    marginBottom: 36,
    paddingHorizontal: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    color: theme.colors.neutralWhite,
    fontSize: 16,
    height: 48,
  },
  faqList: {
    gap: 20,
    marginBottom: 24,
    marginTop: 80,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.whiteOpacity10,
    minHeight: 56,
  },
  faqHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  faqQuestion: {
    color: theme.colors.neutralWhite,
    flex: 1,
    fontSize: 16,
  },
  faqAnswer: {
    color: theme.colors.neutralWhite,
    opacity: 0.7,
    marginTop: 12,
    fontSize: 14,
    marginBottom: 28,
  },
  footerSpace: {
    height: 40,
  },
  footerContainer: {
    backgroundColor: theme.colors.neutralBackground,
    borderTopWidth: 1,
    borderTopColor: theme.colors.whiteOpacity10,
    borderRadius: 0,
    paddingVertical: 24,
    paddingBottom: 24,
  },
  footerContentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    gap: 24,
  },
  footerTextBoxRow: {
    flex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  footerText: {
    color: theme.colors.neutralWhite,
  },
  emailBtnRow: {
    backgroundColor: theme.colors.primary,
    borderRadius: 9999,
    paddingHorizontal: 20,
    paddingVertical: 6,
    minWidth: 82,
    minHeight: 38,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emailBtnTextRow: {
    color: theme.colors.neutralBackground,
    fontSize: 12,
  },
  emailTextRow: {
    color: theme.colors.primary,
    fontSize: 18,
  },
  dropdownIcon: {},
}));

export default HelpSupportScreen;
