import { useCallback, useMemo, useState, useEffect } from 'react';
import { View } from 'react-native';
import { ListRenderItem } from '@shopify/flash-list';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { UserListItem } from '@/components/ui/UserListItem';
import { Spacer } from '@/components/Spacer';
import { IconLoading } from '@/components/IconLoading';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { useGetFollowingInfiniteQuery, type IFollow } from '@/apis/follow';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { Show } from '@/components/Show';
import { Empty } from '@/components/Empty';
import { v4 as uuid } from 'uuid';
const SEARCH_HEIGHT = 104;
interface FollowingTabProps {
  onFollowToggle: (userId: string, isFollowed: boolean) => void;
  searchQuery: string;
  targetUserId?: string;
  isOwner: boolean;
  refetchFollowing: () => void;
  displayUserName: string;
  totalFollowing: number;
}

export const FollowingTab = ({
  onFollowToggle,
  searchQuery,
  targetUserId,
  isOwner,
  refetchFollowing,
  totalFollowing,
  displayUserName,
}: FollowingTabProps) => {
  const { styles } = useStyles(stylesheet);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const { data: userProfile } = useGetProfileQuery();
  const [listKey, setListKey] = useState(uuid());

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const {
    data: followingData,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetFollowingInfiniteQuery({
    targetUserId: Number(targetUserId),
    limit: 20,
    search: debouncedSearchQuery,
  });

  const allFollowing = useMemo(() => {
    const infiniteData = followingData as any;
    if (!infiniteData?.pages) return [];

    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [followingData]);

  const handleFollowToggle = useCallback(
    (userId: string, isFollowed: boolean) => {
      onFollowToggle(userId, isFollowed);
    },
    [onFollowToggle]
  );

  const renderUserItem = useCallback<ListRenderItem<IFollow>>(
    ({ item }) => {
      return (
        <UserListItem
          user={item}
          onUserPress={() =>
            router.push({
              pathname: '/(app)/[userId]',
              params: {
                userId: item?.id?.toString(),
              },
            })
          }
          onFollowToggle={handleFollowToggle}
          showFollowButton={item.id !== userProfile?.id}
        />
      );
    },
    [handleFollowToggle, userProfile?.id]
  );

  const keyExtractor = useCallback((item: IFollow) => item.id?.toString() || '', []);

  const renderSeparator = useCallback(() => <Spacer height={24} />, []);

  const listHeaderComponent = useMemo(
    () => (
      <View>
        <Spacer height={16} />
      </View>
    ),
    []
  );

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchFollowing(), refetch()]);
  }, [refetch, refetchFollowing]);

  return (
    <FlashListAnimate
      data={allFollowing}
      renderItem={renderUserItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={renderSeparator}
      ListHeaderComponent={listHeaderComponent}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      onRefresh={handleRefresh}
      ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
      flashListKey={listKey}
      ListEmptyComponent={
        <Show when={totalFollowing === 0}>
          <Empty
            type='follow'
            emptyText={`${isOwner ? 'You' : displayUserName} ${isOwner ? `haven't` : `hasn't`} followed anyone yet.`}
          />
        </Show>
      }
    />
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: rt.insets.bottom + SEARCH_HEIGHT + 24,
    minHeight: 100,
    marginTop: rt.insets.top + SEARCH_HEIGHT,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
