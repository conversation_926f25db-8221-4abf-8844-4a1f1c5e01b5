import { useCallback, useState } from 'react';
import { View } from 'react-native';
import { Tabs } from 'react-native-collapsible-tab-view';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';

import { Header } from '@/components/ui/Header';
import { CustomTabBarWithSearch, FollowersTab, FollowingTab } from './components';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useGetFollowersQuery, useGetFollowingQuery } from '@/apis/follow';
import { checkIsOwner, getUserIdForFavorites, formatCompactNumber } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { useToggleFollowUserMutation } from '@/apis/user';

export const ProfileFollowersScreen = () => {
  const { styles } = useStyles(stylesheet);
  const [searchQuery, setSearchQuery] = useState('');

  const {
    userId: routerUserId,
    username: routerUsername,
    initialTab,
  } = useLocalSearchParams<{
    userId?: string;
    username?: string;
    initialTab?: string;
  }>();
  const { data: userProfile } = useGetProfileQuery();

  const targetUserId = getUserIdForFavorites(routerUserId, userProfile?.id);
  const isOwner = checkIsOwner(userProfile?.id, targetUserId);
  const { data: followersData, refetch: refetchFollowers } = useGetFollowersQuery({
    targetUserId: Number(targetUserId),
    limit: 1,
  });
  const { data: followingData, refetch: refetchFollowing } = useGetFollowingQuery({
    targetUserId: Number(targetUserId),
    limit: 1,
  });
  const toggleFollowMutation = useToggleFollowUserMutation();

  const handleFollowToggle = (userId: string, isFollowed: boolean) => {
    toggleFollowMutation.mutate(Number(userId), {
      onSuccess: (data) => {
        toastSuccess({
          description: !isFollowed ? 'Followed this user successfully' : 'Unfollowed this user successfully',
        });
        refetchFollowers();
        refetchFollowing();
      },
      onError: (error) => {
        toastError(!isFollowed ? 'Followed this user unsuccessfully' : 'Unfollowed this user unsuccessfully');
      },
    });
  };

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const renderCustomTabBar = useCallback(
    (tabBarProps: any) => (
      <CustomTabBarWithSearch {...tabBarProps} searchQuery={searchQuery} onSearchChange={handleSearchChange} />
    ),
    [searchQuery, handleSearchChange]
  );

  const displayUserName = routerUsername || userProfile?.username || 'Profile';
  const initialTabName = initialTab || 'followers';
  const totalFollowers = followersData?.pagination.totalItems || 0;
  const totalFollowing = followingData?.pagination.totalItems || 0;

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Header isBack title={displayUserName} titleStyle={styles.headerTitle} />
      </View>

      <Tabs.Container
        containerStyle={styles.tabContainer}
        headerContainerStyle={styles.headerContainer}
        lazy={false}
        initialTabName={initialTabName}
        renderTabBar={renderCustomTabBar}
        revealHeaderOnScroll={false}
      >
        <Tabs.Tab
          label={`${formatCompactNumber(totalFollowers)} ${totalFollowers <= 1 ? 'Follower' : 'Followers'}`}
          name='followers'
          key='followers'
        >
          <FollowersTab
            onFollowToggle={handleFollowToggle}
            searchQuery={searchQuery}
            targetUserId={targetUserId}
            isOwner={isOwner}
            refetchFollowers={refetchFollowers}
            displayUserName={displayUserName}
            totalFollowers={totalFollowers}
          />
        </Tabs.Tab>

        <Tabs.Tab label={`${formatCompactNumber(totalFollowing)} Following`} name='following' key='following'>
          <FollowingTab
            onFollowToggle={handleFollowToggle}
            searchQuery={searchQuery}
            targetUserId={targetUserId}
            isOwner={isOwner}
            refetchFollowing={refetchFollowing}
            displayUserName={displayUserName}
            totalFollowing={totalFollowing}
          />
        </Tabs.Tab>
      </Tabs.Container>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top,
  },
  headerTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
    lineHeight: 32,
    ...theme.fw600,
  },
  headerContainer: {
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  tabContainer: {
    overflow: 'hidden',
  },
}));
