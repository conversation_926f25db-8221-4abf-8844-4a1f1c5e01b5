import { useLoginMutation, useSpotifyRequestTokenMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { APP_SCHEME, env } from '@/utils/const';
import { toastError } from '@/utils/toast';
import { ResponseType, makeRedirectUri, useAuthRequest, Prompt } from 'expo-auth-session';
import { router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useCallback, useEffect, useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import AccountUpdatedModal from './AccountUpdatedModal';

WebBrowser.maybeCompleteAuthSession();

const discovery = {
  authorizationEndpoint: 'https://accounts.spotify.com/authorize',
  tokenEndpoint: 'https://accounts.spotify.com/api/token',
};

type Props = {
  isOnlyIcon?: boolean;
  isSignUp?: boolean;
  isAddMethodMode?: boolean;
  isChangeMethodMode?: boolean;
  currentProviderType?: string;
  onMethodAdded?: () => void;
  onMethodChanged?: () => void;
  onMethodCancelled?: () => void;
};

export interface SignInSpotifyRef {
  onLogin: () => void;
}

export const SignInSpotify = forwardRef<SignInSpotifyRef, Props>(
  (
    {
      isOnlyIcon,
      isSignUp,
      isAddMethodMode = false,
      isChangeMethodMode = false,
      currentProviderType,
      onMethodAdded,
      onMethodChanged,
      onMethodCancelled,
    },
    ref
  ) => {
    const setAccessToken = useUserStore.use.setAccessToken();
    const setRefreshToken = useUserStore.use.setRefreshToken();

    const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
    const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
    const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();
    const { mutateAsync: requestTokenMutation, isPending: isPendingRequestToken } = useSpotifyRequestTokenMutation();
    const { styles } = useStyles(stylesheet);

    const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
    const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');

    const signIn = useCallback(
      async (idToken: string) => {
        try {
          if (isAddMethodMode) {
            await addMethodMutation({
              provider: 'spotify',
              token: idToken,
            });
            onMethodAdded?.();
            return;
          }
          if (isChangeMethodMode && currentProviderType === 'spotify') {
            await updateMethodMutation({
              provider: 'spotify',
              token: idToken,
            });
            onMethodChanged?.();
            return;
          }
          const loginResult = await loginMutation({
            provider: 'spotify',
            token: idToken,
          });
          const { user, tokens } = loginResult;
          setRefreshToken(tokens.refreshToken);
          setAccessToken(tokens.accessToken);

          if (router.canDismiss()) router.dismissAll();
          if (user.status === 'complete') {
            router.replace({ pathname: '/(app)/(tabs)' });
          } else if (user.status === 'update_profile') {
            router.replace({ pathname: '/(app)/update-info' });
          } else if (user.status === 'plan_payment') {
            router.replace({ pathname: '/(app)/choose-plan' });
          } else if (user.status === 'choose_interest') {
            router.replace({ pathname: '/(app)/choose-interest' });
          } else if (user.status === 'choose_podcast') {
            router.replace({ pathname: '/(app)/choose-podcast' });
          }
        } catch (error: any) {
          if (error.message == 'Your email account has been updated') {
            setAccountUpdatedToken(idToken);
            setIsAccountUpdatedModalVisible(true);
            return;
          }
          toastError(error);
          if (isChangeMethodMode) {
            onMethodCancelled?.();
          }
        }
      },
      [
        loginMutation,
        addMethodMutation,
        updateMethodMutation,
        isAddMethodMode,
        isChangeMethodMode,
        onMethodAdded,
        onMethodChanged,
        onMethodCancelled,
        setAccessToken,
        setRefreshToken,
        currentProviderType,
      ]
    );

    const redirectUriPath = useMemo(() => {
      if (isChangeMethodMode || isAddMethodMode) {
        return '(app)/settings/account';
      }
      return isSignUp ? 'sign-up' : 'sign-in';
    }, [isChangeMethodMode, isAddMethodMode, isSignUp]);

    const [normalRequest, normalResponse, normalPromptAsync] = useAuthRequest(
      {
        responseType: ResponseType.Code,
        clientId: env.SPOTIFY_CLIENT_ID,
        scopes: ['user-read-email', 'playlist-modify-public'],
        redirectUri: makeRedirectUri({
          scheme: APP_SCHEME,
          path: redirectUriPath,
        }),
        clientSecret: env.SPOTIFY_CLIENT_SECRET,
      },
      discovery
    );

    const [changeRequest, changeResponse, changePromptAsync] = useAuthRequest(
      {
        responseType: ResponseType.Code,
        clientId: env.SPOTIFY_CLIENT_ID,
        scopes: ['user-read-email', 'playlist-modify-public'],
        redirectUri: makeRedirectUri({
          scheme: APP_SCHEME,
          path: redirectUriPath,
        }),
        clientSecret: env.SPOTIFY_CLIENT_SECRET,
        prompt: Prompt.SelectAccount,
      },
      discovery
    );

    const request = isChangeMethodMode ? changeRequest : normalRequest;
    const response = isChangeMethodMode ? changeResponse : normalResponse;
    const promptAsync = isChangeMethodMode ? changePromptAsync : normalPromptAsync;

    const handleSpotifySignIn = useCallback(() => {
      if (!request) {
        return;
      }
      promptAsync()
        .then((result) => {})
        .catch((error) => {
          toastError('Unable to connect to your account');
          if (isChangeMethodMode) {
            onMethodCancelled?.();
          }
        });
    }, [request, promptAsync, isChangeMethodMode, onMethodCancelled]);

    useImperativeHandle(
      ref,
      () => ({
        onLogin: handleSpotifySignIn,
      }),
      [handleSpotifySignIn]
    );

    useEffect(() => {
      if (response?.type === 'cancel') {
        if (isChangeMethodMode || isAddMethodMode) {
          onMethodCancelled?.();
        }
        return;
      }

      if (response?.type === 'error') {
        toastError('Authentication denied');
        if (isChangeMethodMode || isAddMethodMode) {
          onMethodCancelled?.();
        }
        return;
      }

      if (response?.type !== 'success') {
        return;
      }

      (async () => {
        const { code } = response.params;
        if (!request?.codeVerifier || !code) return;

        try {
          const { access_token } = await requestTokenMutation({
            code,
            codeVerifier: request?.codeVerifier,
            redirectUri: makeRedirectUri({
              scheme: APP_SCHEME,
              path: redirectUriPath,
            }),
          });
          await signIn(access_token);
        } catch (error) {
          toastError(error);
          if (isChangeMethodMode || isAddMethodMode) {
            onMethodCancelled?.();
          }
        }
      })();
    }, [
      response,
      redirectUriPath,
      request,
      signIn,
      requestTokenMutation,
      isChangeMethodMode,
      onMethodCancelled,
      isAddMethodMode,
    ]);

    return (
      <>
        <SignInSocialButton
          disabled={!request || isPendingLogin || isPendingRequestToken || isPendingAddMethod || isPendingUpdateMethod}
          onPress={handleSpotifySignIn}
          Icon={<Icons.Spotify size={24} />}
          title='Spotify'
          isOnlyIcon={isOnlyIcon}
          containerStyle={styles.container}
        />
        <AccountUpdatedModal
          isVisible={isAccountUpdatedModalVisible}
          onClose={() => setIsAccountUpdatedModalVisible(false)}
          token={accountUpdatedToken}
          provider='spotify'
        />
      </>
    );
  }
);

SignInSpotify.displayName = 'SignInSpotify';

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.spotify,
  },
}));
