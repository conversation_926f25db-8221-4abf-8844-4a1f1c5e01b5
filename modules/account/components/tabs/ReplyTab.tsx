import { useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountReplyItem } from '../reply/AccountReplyItem';
import { useGetInfiniteReplyUserHistoryQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { IUserProfileById, ReplyHistory } from '@/apis/user';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Spacer } from '@/components/Spacer';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { ListRenderItem } from '@shopify/flash-list';
import { BOTTOM_TAB_HEIGHT } from '@/utils/const';

type Props = {
  profile?: IUserProfileById;
};

export const ReplyTab = ({ profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const userId = profile?.id?.toString();

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: replyHistoryData,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    status,
    isPending,
  } = useGetInfiniteReplyUserHistoryQuery({
    userId: userId ?? '',
    limit: 20,
  });

  const replyHistory = replyHistoryData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<ReplyHistory>>(({ item }) => <AccountReplyItem replyItem={item} />, []);

  const renderItemSeparator = useCallback(() => <View style={styles.separator} />, [styles.separator]);

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: ReplyHistory) => `${item.id}-${item.type}`, []);

  const renderSkeletonComponent = useCallback(() => {
    return <IconLoading />;
  }, []);

  const handleRefetch = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(profile?.id ?? '') }),
      queryClient.resetQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() }),
    ]);
  };

  const isShowEmpty = status === 'success' && replyHistory.length === 0;

  return (
    <TabFlashList
      data={replyHistory}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      renderItem={renderItem}
      onRefresh={handleRefetch}
      refreshing={isPending}
      keyExtractor={keyExtractor}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      contentContainerStyle={styles.contentContainer}
      ItemSeparatorComponent={renderItemSeparator}
      ListHeaderComponent={<Spacer height={24} />}
      ListFooterComponent={isFetchingNextPage ? renderSkeletonComponent : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='post'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} replied to any comments`}
          />
        ) : null
      }
    />
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    // marginTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: BOTTOM_TAB_HEIGHT,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    marginVertical: 24,
  },
}));
