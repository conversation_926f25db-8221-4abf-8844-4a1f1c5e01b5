import { useGetInfiniteUserUpVoteHistoryQuery, useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { memo, useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';
import { ListRenderItem } from '@shopify/flash-list';
import { LikedPostItem } from '../account-likes/components/LikedPostItem';
import { useLocalSearchParams } from 'expo-router';
import { Header } from '@/components/ui/Header';
import { UserUpVoteComment } from '@/apis/user';
import { Spacer } from '@/components/Spacer';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';

const AccountUpVote = memo(() => {
  const { styles } = useStyles(stylesheet);

  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });
  const queryClient = useQueryClient();

  const {
    data: upVotesData,
    isPending,
    isSuccess,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserUpVoteHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPost = upVotesData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<UserUpVoteComment>>(
    ({ item }) => <LikedPostItem likedPostItem={item} />,
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserUpVoteComment) => `${item.parentId}-${item.commentId}`, []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderItemSeparator = useCallback(() => <View style={styles.line} />, [styles.line]);

  const handleRefetch = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.userProfile.getUserUpVoteHistoryInfinite() });
  }, [queryClient]);

  const isShowEmpty = isSuccess && likesPost.length === 0;

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header
          title={isPendingUserProfile ? '' : isYou ? 'My Upvote' : `${userProfileById?.username} Upvote`}
          isBack
        />
      </View>

      <FlashListAnimate
        refreshing={isPending}
        onRefresh={handleRefetch}
        bounces={false}
        data={likesPost}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.4}
        ListHeaderComponent={<Spacer height={24} />}
        ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
        ItemSeparatorComponent={renderItemSeparator}
        ListEmptyComponent={
          isShowEmpty ? (
            <Empty
              type='upvote'
              emptyText={`${isYou ? 'You' : userProfileById?.username} ${isYou ? "haven't" : "hasn't"} upvote any comments`}
            />
          ) : null
        }
      />
    </View>
  );
});

export default AccountUpVote;

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.neutralDarkGrey,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  line: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    marginVertical: 24,
  },
}));
